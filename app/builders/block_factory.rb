class BlockFactory
  def self.build_block_data(source:, locale:)
    data = ConfigAdapter.for(source)
    return nil unless data

    if locale === nil
      locale = :cs
    end

    BlockData.new(
      id: data.id,
      name: data.name,
      options: data.options,
      controls_data: data.controls_data(locale),
      media_items: data.media_items,
      media_options: data.media_options,
      pricing_id: data.pricing_id,
      pricing_options: data.pricing_options,
      background_image: data.background_image_attachment,
      background_image_mobile: data.background_image_mobile_attachment
    )
  end

  # Po<PERSON>dlná zkratka, která vytvoří presenter.
  def self.create_presenter(source:, view_context:)
    block_data = build_block_data(source: source, locale: view_context.locale)
    return nil unless block_data

    BlockPresenter.new(
      id: block_data.id,
      name: block_data.name,
      view_context: view_context,
      options: block_data.options,
      controls: self.build_controls(block_data.controls_data, type: view_context.type),
      media_items: block_data.media_items,
      media_options: block_data.media_options,
      pricing_id: block_data.pricing_id,
      pricing_options: block_data.pricing_options,
      background_image: block_data.background_image,
      background_image_mobile: block_data.background_image_mobile
    )
  end

  def self.build_controls(controls_data, type: :frontend)
    Array(controls_data).map do |control_data|
      ControlRegistry.class_for(control_data[:type])
                     .new(control_data, type)
    end
  end

  def build_controls_data(raw_controls_data)
    Array(raw_controls_data)
  end

  def self.build_component(source:, context:)
    presenter = create_presenter(source: source, view_context: context)
    return nil unless presenter

    component_class = presenter.component_class
    return nil unless component_class

    component_class.new(block_presenter: presenter, view_context: context)
  end

  def self.build_by_type(type, view_context:)
    Rails.application.config.x.components_by_type.fetch(type, {}).map do |key, _|
      self.create_presenter(source: key.to_s, view_context: view_context).component
    end
  end
end