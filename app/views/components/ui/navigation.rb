class Ui::Navigation < ApplicationComponent
  include Phlex::Rails::Helpers::LinkTo

  attr_reader :pages, :current_page, :type

  def initialize(pages:, current_page:, type: :desktop)
    @pages = pages
    @current_page = current_page
    @type = type
  end

  def view_template
    if @type == :mobile
      div(class: "flex gap-x-1 items-center") do
        @pages.each do |page, children|
          if children.any?
            # Pro mobilní verzi zobrazíme jen hlav<PERSON>í stránku bez dropdown
            link_to page.title,
                    page_path_resolver(page),
                    class: mobile_button_classes(page),
                    target: ("_blank" if page.is_link?),
                    data: scroll_data(page)
          else
            link_to page.title,
                    page_path_resolver(page),
                    class: mobile_button_classes(page),
                    target: ("_blank" if page.is_link?),
                    data: scroll_data(page)
          end
        end
      end
    else
      @pages.each do |page, children|
        if children.any?
          div(
            class: "relative",
            data_controller: "dropdown",
            data_action:
              " click@ window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
          ) do
            button(
              data_action: "dropdown#toggle:stop",
              type: "button",
              class: "flex items-center btn btn-ghost hover:bg-base-300",
              aria_expanded: "false"
            ) do
              plain page.title
              svg(
                xmlns: "http://www.w3.org/2000/svg",
                viewbox: "0 0 16 16",
                fill: "currentColor",
                class: "size-4"
              ) do |s|
                s.path(
                  fill_rule: "evenodd",
                  d:
                    "M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z",
                  clip_rule: "evenodd"
                )
              end
            end
            div(data_dropdown_target: "menu", class: "hidden relative z-50") do
              div(
                class:
                  "absolute -left-24 top-full z-10 mt-3 menu bg-base-200 rounded-box w-56 bg-white"
              ) do
                children.each do |child_page, child_pages|
                  div(
                    class: "relative px-2 py-1.5 rounded-box hover:bg-gray-100",
                    data_dropdown_target: "menuItem"
                  ) do
                    a(
                      href: page_path_resolver(child_page),
                      class: "block text-sm font-medium leading-6"
                    ) do
                      plain child_page.title
                      span(class: "absolute inset-0")
                    end
                  end
                end
              end
            end
          end
        else
          link_to page.title,
                  page_path_resolver(page),
                  class: (page.cta? ? "btn btn-accent" : "btn btn-ghost"),
                  target: ("_blank" if page.is_link?),
                  data: scroll_data(page)
        end
      end
    end
  end

  private

  def mobile_button_classes(page)
    base_classes = "btn whitespace-nowrap px-4 py-2 text-sm font-medium"
    if page.cta?
      "#{base_classes} btn-accent"
    else
      "#{base_classes} btn-ghost"
    end
  end

  def scroll_data(page)
    if page.is_anchor? && @current_page&.is_homepage?
      {
        controller: "scroll-to",
        "scroll-to-offset-value": 0,
        "scroll-to-behavior-value": "smooth"
      }
    else
      {}
    end
  end

  def page_path_resolver(page)
    return "#" if controller.present? && admin?
    return page.link if page.is_link?
    return homepage_path if page.is_homepage

    if page.is_anchor? && page.anchor_block.present?
      return "#block-#{page.anchor_block.id}" if page.is_anchor? && page.anchor_block.present?
      return "/#block-#{page.anchor_block.id}" if @current_page != page
    end

    locale = I18n.locale || :cs

    if I18n.locale == Current.website.locale.to_sym
      locale = nil
    end

    page_path(page, locale:)
  end
end