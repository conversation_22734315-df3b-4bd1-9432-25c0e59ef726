module BlockViews
  class HeaderViews::Header002Component < BaseComponent
    include Phlex::Rails::Helpers::Image<PERSON>ag

    def view_template
      container do
        inner_container do
          header(class: "hidden mx-auto md:flex items-center justify-between p-3") do
            nav(class: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative") do
              div(class: "lg:flex items-center justify-center") do
                div(class: "flex items-center gap-x-3") do
                  render Ui::Navigation.new(
                    pages: pages_on_left,
                    current_page: @context.current_page
                  )
                end if @context.pages_for_nav
                div(class: "relative") do
                  div(class: "bg-white rounded") do
                    a(
                      href: root_path,
                      class: %(#{Current.website.logo_size_class} flex)
                    ) { image_tag Current.website.logo, class: "w-auto object-cover" }
                  end
                end
                div(class: "flex items-center gap-x-3") do
                  render Ui::Navigation.new(
                    pages: pages_on_right,
                    current_page: @context.current_page
                  )
                end if @context.pages_for_nav
              end
            end
          end

          header(class: "flex flex-col md:hidden mx-auto items-center justify-center p-3") do
            div(class: "flex lg:flex-1") do
              if Current.website.logo.variable?
              else
                a(href: "/", class: "#{Current.website.logo_size_class} flex") do
                  image_tag Current.website.logo, class: "w-auto object-cover"
                end
              end
            end

            if @context.pages_for_nav&.any?
              div(class: "w-full overflow-x-auto scrollbar-hide") do
                div(class: "flex justify-center min-w-max px-4 py-2") do
                  render Ui::Navigation.new(
                    pages: @context.pages_for_nav,
                    current_page: @context.current_page,
                    type: :mobile
                  )
                end
              end
            end
          end
        end
      end
    end

    private

    def pages_on_left
      memoized_split_pages.first
    end

    def pages_on_right
      memoized_split_pages.last
    end

    def memoized_split_pages
      @memoized_split_pages ||= begin
                                  all_pages = @context.pages_for_nav.to_a
                                  split_at = (all_pages.length / 2.0).ceil
                                  left, right = all_pages.each_slice(split_at).to_a
                                  [left.to_h, right.to_h]
                                end
    end

    def page_path_resolver(page)
      return "#" if controller.present? && admin?
      return page.link if page.is_link?
      return homepage_path if page.is_homepage

      if page.is_anchor? && page.anchor_block.present?
        return "#block-#{page.anchor_block.id}" if page.is_anchor? && page.anchor_block.present?
        return "/#block-#{page.anchor_block.id}" if @current_page != page
      end

      locale = I18n.locale || :cs

      if I18n.locale == Current.website.locale.to_sym
        locale = nil
      end

      page_path(page, locale:)
    end
  end
end