# app/services/block_config_adapter.rb
class BlockConfigAdapter < ConfigAdapter
  def initialize(block_record) # Přejmenováno z 'block' na 'block_record' pro jasnost
    @block = block_record
  end

  def id
    @block.id
  end

  def name
    @block.name
  end

  def options
    # Zajistíme deep_symbolize_keys pro konzistenci
    (@block.options || {}).deep_symbolize_keys
  end

  def controls_data(locale)
    @block.controls_for_locale(locale).map do |control|
      {
        type: control.type,
        id: control.id,
        text: control.text,
        options: (control.options || {}).deep_symbolize_keys, # Zajistíme symbolizaci i zde
        position: control.position,
        locale: control.locale
      }
    end
  end

  def media_items
    return nil if @block.media.empty?

    media_items = []
    @block.media.each do |media_item|
      media_items << MediaItemData.new(
        id: media_item.id,
        title: media_item.title,
        text: media_item.text,
        icon: media_item.icon,
        published_at: media_item.published_at,
        image_attachment: media_item.image.attached? ? media_item.image : nil,
        **media_item[:custom_fields] || {}
      )
    end

    media_items
  end

  def media_options
    MediaOptionsData.new(
      inline_items_count: @block.media_inline_items_count,
      posts_limit: @block.media_posts_limit,
      position: @block.media_position,
      gap: @block.media_gap,
      type: @block.media_type&.slug,
      layout: @block.media_layout
    )
  end

  def pricing_id
    @block.pricing_id
  end

  def pricing_options
    (@block.pricing_options || {}).deep_symbolize_keys
  end

  def background_image_attachment
    @block.background_image if @block.background_image.attached? # Kontrola attached? je dobrá
  end

  def background_image_mobile_attachment
    @block.background_image_mobile if @block.background_image_mobile.attached?
  end
end